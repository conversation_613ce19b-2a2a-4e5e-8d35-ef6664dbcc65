"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/foods/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/dashboard/foods/page.tsx":
/*!**************************************************!*\
  !*** ./app/(dashboard)/dashboard/foods/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FoodsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,ArrowUpDown,ChevronLeft,ChevronRight,Edit,Image,MoreHorizontal,Plus,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_foods__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-foods */ \"(app-pages-browser)/./hooks/use-foods.tsx\");\n/* harmony import */ var _hooks_use_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-upload */ \"(app-pages-browser)/./hooks/use-upload.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _types_schema__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/types/schema */ \"(app-pages-browser)/./types/schema.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Hàm tiện ích để hiển thị tên danh mục dễ đọc\nconst formatCategoryName = (category)=>{\n    return category.replace(/_/g, ' ');\n};\n// Schema validation cho form thêm món ăn\nconst foodFormSchema = zod__WEBPACK_IMPORTED_MODULE_15__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_15__.string().min(1, {\n        message: \"Tên món ăn không được để trống\"\n    }),\n    price: zod__WEBPACK_IMPORTED_MODULE_15__.number().min(0.01, {\n        message: \"Giá phải lớn hơn 0\"\n    }),\n    category: zod__WEBPACK_IMPORTED_MODULE_15__.nativeEnum(_types_schema__WEBPACK_IMPORTED_MODULE_14__.FoodCategory),\n    isAvailable: zod__WEBPACK_IMPORTED_MODULE_15__.boolean().default(true),\n    imageUrl: zod__WEBPACK_IMPORTED_MODULE_15__.string().optional()\n});\nfunction FoodsPage() {\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ALL\");\n    const [isAddDialogOpen, setIsAddDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingItem, setEditingItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5) // Giảm xuống 5 item mỗi trang\n    ;\n    const [priceSortOrder, setPriceSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('none') // State cho sắp xếp giá\n    ;\n    const { uploadImage, isUploading } = (0,_hooks_use_upload__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { foods, isLoading, error, toggleAvailability, deleteFood, createFood, updateFood } = (0,_hooks_use_foods__WEBPACK_IMPORTED_MODULE_8__.useFood)();\n    // Form cho thêm mới món ăn\n    const addForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(foodFormSchema),\n        defaultValues: {\n            name: '',\n            price: 0,\n            category: _types_schema__WEBPACK_IMPORTED_MODULE_14__.FoodCategory.MAIN_COURSE,\n            isAvailable: true,\n            imageUrl: ''\n        }\n    });\n    // Form cho chỉnh sửa món ăn\n    const editForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(foodFormSchema),\n        defaultValues: {\n            name: '',\n            price: 0,\n            category: _types_schema__WEBPACK_IMPORTED_MODULE_14__.FoodCategory.MAIN_COURSE,\n            isAvailable: true,\n            imageUrl: ''\n        }\n    });\n    const handleFileChange = async (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        setSelectedFile(file);\n        try {\n            const imageUrl = await uploadImage(file);\n            setPreviewImage(imageUrl);\n            // Cập nhật giá trị imageUrl trong form\n            if (isEditDialogOpen) {\n                editForm.setValue('imageUrl', imageUrl);\n            } else {\n                addForm.setValue('imageUrl', imageUrl);\n            }\n        } catch (err) {\n            console.error('Error uploading image:', err);\n        }\n    };\n    // Xử lý thêm mới món ăn\n    const handleAddFood = async (data)=>{\n        try {\n            const foodData = {\n                name: data.name,\n                price: data.price,\n                category: data.category,\n                isAvailable: data.isAvailable,\n                imageUrl: data.imageUrl\n            };\n            await createFood(foodData);\n            setIsAddDialogOpen(false);\n            addForm.reset();\n            setPreviewImage(null);\n            setSelectedFile(null);\n        } catch (error) {\n            console.error('Lỗi khi thêm món ăn:', error);\n        }\n    };\n    // Xử lý chỉnh sửa món ăn\n    const handleEditFood = async (data)=>{\n        if (!editingItem) return;\n        try {\n            const foodData = {\n                name: data.name,\n                price: data.price,\n                category: data.category,\n                isAvailable: data.isAvailable,\n                imageUrl: data.imageUrl\n            };\n            await updateFood(editingItem.id, foodData);\n            setIsEditDialogOpen(false);\n            setEditingItem(null);\n            setPreviewImage(null);\n            setSelectedFile(null);\n        } catch (error) {\n            console.error('Lỗi khi cập nhật món ăn:', error);\n        }\n    };\n    // Mở dialog chỉnh sửa và điền thông tin món ăn\n    const openEditDialog = (item)=>{\n        setEditingItem(item);\n        setPreviewImage(item.imageUrl || null);\n        editForm.reset({\n            name: item.name,\n            price: item.price,\n            category: item.category || _types_schema__WEBPACK_IMPORTED_MODULE_14__.FoodCategory.MAIN_COURSE,\n            isAvailable: item.isAvailable,\n            imageUrl: item.imageUrl || ''\n        });\n        setIsEditDialogOpen(true);\n    };\n    // Xử lý thay đổi trạng thái món ăn\n    const handleAvailabilityChange = async (id, isAvailable)=>{\n        try {\n            await toggleAvailability(id, isAvailable);\n        } catch (error) {\n            console.error('Lỗi khi cập nhật trạng thái món ăn:', error);\n        }\n    };\n    // Xử lý xóa món ăn\n    const handleDelete = async (id)=>{\n        try {\n            await deleteFood(id);\n        } catch (error) {\n            console.error('Lỗi khi xóa món ăn:', error);\n        }\n    };\n    // Xử lý sắp xếp theo giá\n    const handlePriceSort = ()=>{\n        if (priceSortOrder === 'none') {\n            setPriceSortOrder('asc');\n        } else if (priceSortOrder === 'asc') {\n            setPriceSortOrder('desc');\n        } else {\n            setPriceSortOrder('none');\n        }\n        setCurrentPage(1) // Reset về trang đầu khi sắp xếp\n        ;\n    };\n    // Lọc món ăn theo từ khóa tìm kiếm và danh mục\n    const filteredFoods = foods.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.price.toString().includes(searchTerm);\n        const matchesCategory = selectedCategory === \"ALL\" || item.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    // Áp dụng sắp xếp theo giá\n    const sortedFoods = [\n        ...filteredFoods\n    ].sort((a, b)=>{\n        if (priceSortOrder === 'asc') {\n            return a.price - b.price;\n        } else if (priceSortOrder === 'desc') {\n            return b.price - a.price;\n        }\n        return 0 // Không sắp xếp\n        ;\n    });\n    // Phân trang\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentItems = sortedFoods.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(sortedFoods.length / itemsPerPage);\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"Foods\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Manage your restaurant menu\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                        open: isAddDialogOpen,\n                        onOpenChange: setIsAddDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>setIsAddDialogOpen(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Add Food\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                children: \"Th\\xeam m\\xf3n ăn mới\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                children: \"Điền th\\xf4ng tin để th\\xeam m\\xf3n ăn mới v\\xe0o thực đơn.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.Form, {\n                                        ...addForm,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: addForm.handleSubmit(handleAddFood),\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                    control: addForm.control,\n                                                    name: \"name\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                    children: \"T\\xean m\\xf3n ăn\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        placeholder: \"Nhập t\\xean m\\xf3n ăn\",\n                                                                        ...field\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 25\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 23\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                    control: addForm.control,\n                                                    name: \"price\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                    children: \"Gi\\xe1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        type: \"number\",\n                                                                        placeholder: \"Nhập gi\\xe1 m\\xf3n ăn\",\n                                                                        ...field,\n                                                                        onChange: (e)=>field.onChange(parseFloat(e.target.value) || 0),\n                                                                        onFocus: (e)=>{\n                                                                            if (e.target.value === '0') {\n                                                                                e.target.value = '';\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 25\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 23\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                    control: addForm.control,\n                                                    name: \"category\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                    children: \"Danh mục\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    defaultValue: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                    placeholder: \"Chọn danh mục m\\xf3n ăn\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                    lineNumber: 328,\n                                                                                    columnNumber: 29\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                            children: Object.values(_types_schema__WEBPACK_IMPORTED_MODULE_14__.FoodCategory).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                    value: category,\n                                                                                    children: formatCategoryName(category)\n                                                                                }, category, false, {\n                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                    lineNumber: 333,\n                                                                                    columnNumber: 29\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 23\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                    control: addForm.control,\n                                                    name: \"isAvailable\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                            className: \"flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-0.5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                            children: \"Trạng th\\xe1i\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormDescription, {\n                                                                            children: \"M\\xf3n ăn c\\xf3 sẵn s\\xe0ng để phục vụ kh\\xf4ng?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                                        checked: field.value,\n                                                                        onCheckedChange: field.onChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 25\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                    control: addForm.control,\n                                                    name: \"imageUrl\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                    children: \"H\\xecnh ảnh m\\xf3n ăn\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                type: \"file\",\n                                                                                accept: \"image/*\",\n                                                                                onChange: handleFileChange,\n                                                                                disabled: isUploading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                lineNumber: 366,\n                                                                                columnNumber: 27\n                                                                            }, void 0),\n                                                                            previewImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative w-full h-48\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: previewImage,\n                                                                                    alt: \"Preview\",\n                                                                                    className: \"w-full h-full object-cover rounded-md\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                    lineNumber: 374,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 25\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>{\n                                                                setIsAddDialogOpen(false);\n                                                                setPreviewImage(null);\n                                                                setSelectedFile(null);\n                                                            },\n                                                            children: \"Hủy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"submit\",\n                                                            disabled: !addForm.formState.isValid || addForm.formState.isSubmitting || isUploading,\n                                                            children: addForm.formState.isSubmitting ? 'Đang thêm...' : 'Thêm món ăn'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 max-w-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                type: \"search\",\n                                placeholder: \"Search foods...\",\n                                className: \"pl-8\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                        value: selectedCategory,\n                        onValueChange: setSelectedCategory,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                className: \"w-[180px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                    placeholder: \"Select category\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                        value: \"ALL\",\n                                        children: \"All Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.values(_types_schema__WEBPACK_IMPORTED_MODULE_14__.FoodCategory).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                            value: category,\n                                            children: formatCategoryName(category)\n                                        }, category, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: [\n                    \"Error: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-md border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                    className: \"table-fixed w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"w-[30%]\",\n                                        children: \"Name\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"w-[15%]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"h-auto p-0 font-medium hover:bg-transparent\",\n                                            onClick: handlePriceSort,\n                                            children: [\n                                                \"Price\",\n                                                priceSortOrder === 'asc' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"ml-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 48\n                                                }, this),\n                                                priceSortOrder === 'desc' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"ml-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 49\n                                                }, this),\n                                                priceSortOrder === 'none' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"ml-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"w-[20%]\",\n                                        children: \"Image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"w-[15%]\",\n                                        children: \"Availability\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"w-[20%] text-right\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    colSpan: 5,\n                                    className: \"h-24 text-center\",\n                                    children: \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 15\n                            }, this) : filteredFoods.length > 0 ? currentItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"font-medium w-[30%]\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"w-[15%]\",\n                                            children: [\n                                                \"$\",\n                                                item.price.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"w-[20%]\",\n                                            children: item.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: item.imageUrl,\n                                                alt: item.name,\n                                                className: \"w-16 h-16 object-cover rounded-md\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-muted rounded-md flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"w-[15%]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        checked: item.isAvailable,\n                                                        onCheckedChange: (checked)=>handleAvailabilityChange(item.id, checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"min-w-[80px]\",\n                                                        children: item.isAvailable ? 'Available' : 'Unavailable'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"w-[20%] text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Actions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                                open: isEditDialogOpen && (editingItem === null || editingItem === void 0 ? void 0 : editingItem.id) === item.id,\n                                                                onOpenChange: (open)=>{\n                                                                    if (!open) {\n                                                                        setIsEditDialogOpen(false);\n                                                                        setEditingItem(null);\n                                                                        setPreviewImage(null);\n                                                                        setSelectedFile(null);\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                                            onSelect: (e)=>{\n                                                                                e.preventDefault();\n                                                                                openEditDialog(item);\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Edit\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                        children: \"Chỉnh sửa m\\xf3n ăn\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                        lineNumber: 534,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                                                        children: [\n                                                                                            'Chỉnh sửa th\\xf4ng tin m\\xf3n ăn \"',\n                                                                                            item.name,\n                                                                                            '\"'\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                        lineNumber: 535,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            editingItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.Form, {\n                                                                                ...editForm,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                                                    onSubmit: editForm.handleSubmit(handleEditFood),\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                                                            control: editForm.control,\n                                                                                            name: \"name\",\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                                                            children: \"T\\xean m\\xf3n ăn\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 550,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                                placeholder: \"Nhập t\\xean m\\xf3n ăn\",\n                                                                                                                ...field\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                lineNumber: 552,\n                                                                                                                columnNumber: 43\n                                                                                                            }, void 0)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 551,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 554,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                    lineNumber: 549,\n                                                                                                    columnNumber: 39\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                            lineNumber: 545,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                                                            control: editForm.control,\n                                                                                            name: \"price\",\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                                                            children: \"Gi\\xe1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 563,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                                type: \"number\",\n                                                                                                                placeholder: \"Nhập gi\\xe1 m\\xf3n ăn\",\n                                                                                                                ...field,\n                                                                                                                onChange: (e)=>field.onChange(parseFloat(e.target.value) || 0),\n                                                                                                                onFocus: (e)=>{\n                                                                                                                    if (e.target.value === '0') {\n                                                                                                                        e.target.value = '';\n                                                                                                                    }\n                                                                                                                }\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                lineNumber: 565,\n                                                                                                                columnNumber: 43\n                                                                                                            }, void 0)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 564,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 579,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                    lineNumber: 562,\n                                                                                                    columnNumber: 39\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                            lineNumber: 558,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                                                            control: editForm.control,\n                                                                                            name: \"category\",\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                                                            children: \"Danh mục\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 588,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                                            onValueChange: field.onChange,\n                                                                                                            defaultValue: field.value,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                                            placeholder: \"Chọn danh mục m\\xf3n ăn\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                            lineNumber: 595,\n                                                                                                                            columnNumber: 47\n                                                                                                                        }, void 0)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                        lineNumber: 594,\n                                                                                                                        columnNumber: 45\n                                                                                                                    }, void 0)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                    lineNumber: 593,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, void 0),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                                    children: Object.values(_types_schema__WEBPACK_IMPORTED_MODULE_14__.FoodCategory).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                            value: category,\n                                                                                                                            children: formatCategoryName(category)\n                                                                                                                        }, category, false, {\n                                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                            lineNumber: 600,\n                                                                                                                            columnNumber: 47\n                                                                                                                        }, void 0))\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                    lineNumber: 598,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, void 0)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 589,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 606,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                    lineNumber: 587,\n                                                                                                    columnNumber: 39\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                            lineNumber: 583,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                                                            control: editForm.control,\n                                                                                            name: \"isAvailable\",\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                                                                    className: \"flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"space-y-0.5\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                                                                    children: \"Trạng th\\xe1i\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                    lineNumber: 616,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, void 0),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormDescription, {\n                                                                                                                    children: \"M\\xf3n ăn c\\xf3 sẵn s\\xe0ng để phục vụ kh\\xf4ng?\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                    lineNumber: 617,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, void 0)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 615,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                                                                                checked: field.value,\n                                                                                                                onCheckedChange: field.onChange\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                lineNumber: 622,\n                                                                                                                columnNumber: 43\n                                                                                                            }, void 0)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 621,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                    lineNumber: 614,\n                                                                                                    columnNumber: 39\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                            lineNumber: 610,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                                                                            control: editForm.control,\n                                                                                            name: \"imageUrl\",\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormLabel, {\n                                                                                                            children: \"H\\xecnh ảnh m\\xf3n ăn\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 635,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"flex flex-col gap-2\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                                        type: \"file\",\n                                                                                                                        accept: \"image/*\",\n                                                                                                                        onChange: handleFileChange,\n                                                                                                                        disabled: isUploading\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                        lineNumber: 638,\n                                                                                                                        columnNumber: 45\n                                                                                                                    }, void 0),\n                                                                                                                    previewImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"relative w-full h-48\",\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                            src: previewImage,\n                                                                                                                            alt: \"Preview\",\n                                                                                                                            className: \"w-full h-full object-cover rounded-md\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                            lineNumber: 646,\n                                                                                                                            columnNumber: 49\n                                                                                                                        }, void 0)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                        lineNumber: 645,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, void 0)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                                lineNumber: 637,\n                                                                                                                columnNumber: 43\n                                                                                                            }, void 0)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 636,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                            lineNumber: 655,\n                                                                                                            columnNumber: 41\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                    lineNumber: 634,\n                                                                                                    columnNumber: 39\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                            lineNumber: 630,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                                    type: \"button\",\n                                                                                                    variant: \"outline\",\n                                                                                                    onClick: ()=>{\n                                                                                                        setIsEditDialogOpen(false);\n                                                                                                        setEditingItem(null);\n                                                                                                        setPreviewImage(null);\n                                                                                                        setSelectedFile(null);\n                                                                                                    },\n                                                                                                    children: \"Hủy\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                    lineNumber: 660,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                                    type: \"submit\",\n                                                                                                    disabled: !editForm.formState.isValid || editForm.formState.isSubmitting || isUploading,\n                                                                                                    children: editForm.formState.isSubmitting ? 'Đang lưu...' : 'Lưu thay đổi'\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                                    lineNumber: 672,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                            lineNumber: 659,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialog, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                                            className: \"text-destructive\",\n                                                                            onSelect: (e)=>e.preventDefault(),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                    lineNumber: 690,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Delete\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 685,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogHeader, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogTitle, {\n                                                                                        children: \"X\\xe1c nhận x\\xf3a\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                        lineNumber: 696,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogDescription, {\n                                                                                        children: [\n                                                                                            'Bạn c\\xf3 chắc chắn muốn x\\xf3a m\\xf3n ăn \"',\n                                                                                            item.name,\n                                                                                            '\"? H\\xe0nh động n\\xe0y kh\\xf4ng thể ho\\xe0n t\\xe1c.'\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                        lineNumber: 697,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                lineNumber: 695,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogFooter, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogCancel, {\n                                                                                        children: \"Hủy\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                        lineNumber: 703,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_12__.AlertDialogAction, {\n                                                                                        onClick: ()=>handleDelete(item.id),\n                                                                                        className: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n                                                                                        children: \"X\\xf3a\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                        lineNumber: 704,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    colSpan: 5,\n                                    className: \"h-24 text-center\",\n                                    children: \"No foods found.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    \"Showing \",\n                                    indexOfFirstItem + 1,\n                                    \"-\",\n                                    Math.min(indexOfLastItem, filteredFoods.length),\n                                    \" of\",\n                                    ' ',\n                                    filteredFoods.length,\n                                    \" items\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                value: itemsPerPage.toString(),\n                                onValueChange: (value)=>{\n                                    setItemsPerPage(parseInt(value));\n                                    setCurrentPage(1) // Reset to first page when changing items per page\n                                    ;\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                        className: \"h-8 w-[70px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                            placeholder: itemsPerPage.toString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                value: \"5\",\n                                                children: \"5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                value: \"10\",\n                                                children: \"10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                value: \"20\",\n                                                children: \"20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                value: \"50\",\n                                                children: \"50\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"per page\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handlePageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Previous Page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: (()=>{\n                                    // Logic để hiển thị tối đa 10 trang\n                                    const maxVisiblePages = 5; // Số trang hiển thị ở mỗi bên của trang hiện tại\n                                    const pages = [];\n                                    // Luôn hiển thị trang đầu tiên\n                                    if (currentPage > 1 + maxVisiblePages) {\n                                        pages.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handlePageChange(1),\n                                            children: \"1\"\n                                        }, 1, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 21\n                                        }, this));\n                                        // Thêm dấu ... nếu cần\n                                        if (currentPage > 2 + maxVisiblePages) {\n                                            pages.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                disabled: true,\n                                                children: \"...\"\n                                            }, \"ellipsis-start\", false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 23\n                                            }, this));\n                                        }\n                                    }\n                                    // Tính toán phạm vi trang hiển thị\n                                    const startPage = Math.max(1, currentPage - maxVisiblePages);\n                                    const endPage = Math.min(totalPages, currentPage + maxVisiblePages);\n                                    // Thêm các trang trong phạm vi\n                                    for(let i = startPage; i <= endPage; i++){\n                                        pages.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: currentPage === i ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handlePageChange(i),\n                                            children: i\n                                        }, i, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 21\n                                        }, this));\n                                    }\n                                    // Luôn hiển thị trang cuối cùng\n                                    if (currentPage < totalPages - maxVisiblePages) {\n                                        // Thêm dấu ... nếu cần\n                                        if (currentPage < totalPages - 1 - maxVisiblePages) {\n                                            pages.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                disabled: true,\n                                                children: \"...\"\n                                            }, \"ellipsis-end\", false, {\n                                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 23\n                                            }, this));\n                                        }\n                                        pages.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: currentPage === totalPages ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handlePageChange(totalPages),\n                                            children: totalPages\n                                        }, totalPages, false, {\n                                            fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 21\n                                        }, this));\n                                    }\n                                    return pages;\n                                })()\n                            }, void 0, false, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handlePageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_ArrowUpDown_ChevronLeft_ChevronRight_Edit_Image_MoreHorizontal_Plus_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Next Page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n                lineNumber: 730,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\btl-lap-trinh-di-dong\\\\apps\\\\admin-dashboard\\\\app\\\\(dashboard)\\\\dashboard\\\\foods\\\\page.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s(FoodsPage, \"R2WnmZq4EpmdXBcNlxhVrAQ/hYY=\", false, function() {\n    return [\n        _hooks_use_upload__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_use_foods__WEBPACK_IMPORTED_MODULE_8__.useFood,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm\n    ];\n});\n_c = FoodsPage;\nvar _c;\n$RefreshReg$(_c, \"FoodsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/dashboard/foods/page.tsx\n"));

/***/ })

});